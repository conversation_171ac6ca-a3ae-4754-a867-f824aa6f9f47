/**
 * Test script for zoom-to-fit functionality
 * Run this in the browser console after the solar system loads
 */

// Test the zoom-to-fit functionality
function testZoomToFit() {
  console.log('🧪 Testing zoom-to-fit functionality...');
  
  // Check if the app is available
  if (!window.solarSystemApp) {
    console.error('❌ Solar system app not found. Make sure the app is loaded.');
    return;
  }
  
  const app = window.solarSystemApp;
  const sceneManager = app.sceneManager;
  
  if (!sceneManager) {
    console.error('❌ Scene manager not found.');
    return;
  }
  
  // Test getting celestial bodies
  const celestialBodies = sceneManager.getAllCelestialBodies();
  console.log(`📊 Found ${celestialBodies.length} celestial bodies`);
  
  if (celestialBodies.length === 0) {
    console.error('❌ No celestial bodies found.');
    return;
  }
  
  // Test bounds calculation
  try {
    const { calculateSolarSystemBounds } = await import('./src/js/utils/CameraUtils.js');
    const bounds = calculateSolarSystemBounds(celestialBodies, true);
    console.log('📏 Solar system bounds:', bounds);
    
    // Test zoom to fit
    console.log('🎬 Testing zoom to fit...');
    sceneManager.zoomToFit(1000) // 1 second for quick test
      .then(() => {
        console.log('✅ Zoom to fit test completed successfully!');
      })
      .catch(error => {
        console.error('❌ Zoom to fit test failed:', error);
      });
      
  } catch (error) {
    console.error('❌ Error importing CameraUtils:', error);
  }
}

// Test the UI button
function testZoomToFitButton() {
  console.log('🧪 Testing zoom-to-fit button...');
  
  const button = document.getElementById('zoom-to-fit-button');
  if (!button) {
    console.error('❌ Zoom-to-fit button not found in DOM.');
    return;
  }
  
  console.log('✅ Zoom-to-fit button found:', button);
  
  // Simulate button click
  console.log('🖱️ Simulating button click...');
  button.click();
}

// Test keyboard shortcut
function testKeyboardShortcut() {
  console.log('🧪 Testing keyboard shortcut (F key)...');
  
  const event = new KeyboardEvent('keydown', {
    key: 'f',
    code: 'KeyF',
    bubbles: true
  });
  
  document.dispatchEvent(event);
  console.log('⌨️ F key event dispatched');
}

// Run all tests
function runAllTests() {
  console.log('🚀 Running all zoom-to-fit tests...');
  
  setTimeout(() => testZoomToFit(), 1000);
  setTimeout(() => testZoomToFitButton(), 2000);
  setTimeout(() => testKeyboardShortcut(), 3000);
}

// Export functions for manual testing
window.testZoomToFit = testZoomToFit;
window.testZoomToFitButton = testZoomToFitButton;
window.testKeyboardShortcut = testKeyboardShortcut;
window.runAllTests = runAllTests;

console.log('🧪 Zoom-to-fit test functions loaded. Run runAllTests() to test everything.');
