/**
 * CameraUtils - Utility functions for camera positioning and bounding box calculations
 */

import * as THREE from 'three';

/**
 * Calculate practical bounds for zoom-to-fit based on actual rendered positions
 */
export function calculateSolarSystemBounds(celestialBodies, includeOrbits = true, mode = 'exploration') {
  // Validate input
  if (!celestialBodies || celestialBodies.length === 0) {
    console.warn('⚠️ No celestial bodies provided for bounds calculation');
    return createFallbackBounds(mode);
  }

  // Find the furthest planet from the sun (origin)
  let furthestDistance = 0;
  let furthestBody = null;
  let validBodiesCount = 0;

  celestialBodies.forEach(body => {
    if (body.name === 'sun') return; // Skip the sun

    try {
      const position = body.getPosition();

      // Validate position
      if (!position || !isFinite(position.x) || !isFinite(position.y) || !isFinite(position.z)) {
        console.warn(`⚠️ Invalid position for ${body.name}:`, position);
        return;
      }

      const distanceFromSun = position.length();

      // Validate distance
      if (!isFinite(distanceFromSun) || distanceFromSun < 0) {
        console.warn(`⚠️ Invalid distance for ${body.name}: ${distanceFromSun}`);
        return;
      }

      validBodiesCount++;

      if (distanceFromSun > furthestDistance) {
        furthestDistance = distanceFromSun;
        furthestBody = body;
      }
    } catch (error) {
      console.warn(`⚠️ Error getting position for ${body.name}:`, error);
    }
  });

  // Check if we have valid bodies
  if (validBodiesCount === 0 || furthestDistance === 0) {
    console.warn('⚠️ No valid celestial bodies found, using fallback bounds');
    return createFallbackBounds(mode);
  }

  // Mode-specific bounds calculation
  let systemRadius;
  let minSystemRadius;

  switch (mode) {
    case 'realistic':
      // In realistic mode, distances are huge (1 AU = 1000 units)
      // Use a more conservative approach to prevent excessive zoom-out
      systemRadius = Math.min(furthestDistance * 1.2, 2000); // Cap at 2000 units
      minSystemRadius = 500; // Larger minimum for realistic mode
      break;

    case 'exploration':
      // Standard exploration mode (1 AU = 50 units)
      systemRadius = furthestDistance * 1.3; // 30% padding
      minSystemRadius = 100; // Standard minimum
      break;

    case 'artistic':
      // Artistic mode with compressed distances (1 AU = 25 units)
      systemRadius = furthestDistance * 1.4; // More padding for artistic view
      minSystemRadius = 80; // Smaller minimum for closer view
      break;

    default:
      // Fallback to exploration mode
      systemRadius = furthestDistance * 1.3;
      minSystemRadius = 100;
  }

  const finalRadius = Math.max(systemRadius, minSystemRadius);

  // Create symmetric bounds around the origin (sun)
  const bounds = {
    min: new THREE.Vector3(-finalRadius, -finalRadius, -finalRadius),
    max: new THREE.Vector3(finalRadius, finalRadius, finalRadius)
  };

  const center = new THREE.Vector3(0, 0, 0); // Solar system centered at origin
  const size = new THREE.Vector3(finalRadius * 2, finalRadius * 2, finalRadius * 2);
  const maxDimension = finalRadius * 2;

  console.log('📊 Solar system bounds calculated:', {
    mode: mode,
    furthestPlanet: furthestBody?.name || 'none',
    furthestDistance: furthestDistance.toFixed(2),
    systemRadius: finalRadius.toFixed(2),
    maxDimension: maxDimension.toFixed(2),
    bodyCount: celestialBodies.length,
    cappedInRealistic: mode === 'realistic' && systemRadius === 2000
  });

  return {
    min: bounds.min,
    max: bounds.max,
    center,
    size,
    maxDimension,
    systemRadius: finalRadius,
    furthestDistance,
    mode
  };
}

/**
 * Calculate optimal camera position using practical visualization scale
 */
export function calculateOptimalCameraPosition(bounds, camera, padding = 1.5) {
  const { center, systemRadius, mode } = bounds;

  // Mode-specific distance calculation
  let baseDistance = systemRadius * padding;
  let minDistance, maxDistance;

  switch (mode) {
    case 'realistic':
      // In realistic mode, be more conservative with distances
      minDistance = Math.max(systemRadius * 1.1, 200); // Minimum 200 units
      maxDistance = Math.min(systemRadius * 2.5, 1500); // Cap at 1500 units
      break;

    case 'exploration':
      // Standard exploration mode distances
      minDistance = Math.max(systemRadius * 1.2, 150); // Minimum 150 units
      maxDistance = systemRadius * 3; // Up to 3x system radius
      break;

    case 'artistic':
      // Closer distances for artistic mode
      minDistance = Math.max(systemRadius * 1.1, 100); // Minimum 100 units
      maxDistance = systemRadius * 2.5; // Up to 2.5x system radius
      break;

    default:
      // Fallback to exploration mode
      minDistance = Math.max(systemRadius * 1.2, 150);
      maxDistance = systemRadius * 3;
  }

  const finalDistance = Math.max(minDistance, Math.min(baseDistance, maxDistance));

  // Position camera at a good viewing angle
  // Use a moderate elevation for nice perspective
  const elevationAngle = Math.PI / 6; // 30 degrees
  const azimuthAngle = Math.PI / 4; // 45 degrees around Y axis

  // Calculate position using spherical coordinates
  const horizontalRadius = finalDistance * Math.cos(elevationAngle);
  const height = finalDistance * Math.sin(elevationAngle);

  const cameraPosition = new THREE.Vector3(
    center.x + horizontalRadius * Math.cos(azimuthAngle),
    center.y + height,
    center.z + horizontalRadius * Math.sin(azimuthAngle)
  );

  console.log('📹 Calculated camera position:', {
    mode: mode || 'unknown',
    systemRadius: systemRadius.toFixed(2),
    finalDistance: finalDistance.toFixed(2),
    position: cameraPosition.toArray().map(v => v.toFixed(2)),
    lookAt: center.toArray().map(v => v.toFixed(2)),
    elevationDegrees: (elevationAngle * 180 / Math.PI).toFixed(1),
    azimuthDegrees: (azimuthAngle * 180 / Math.PI).toFixed(1),
    distanceCapped: finalDistance === maxDistance
  });

  return {
    position: cameraPosition,
    lookAt: center,
    distance: finalDistance,
    bounds
  };
}

/**
 * Create fallback bounds when calculation fails
 */
function createFallbackBounds(mode = 'exploration') {
  let fallbackRadius;

  switch (mode) {
    case 'realistic':
      fallbackRadius = 1000; // Large fallback for realistic mode
      break;
    case 'exploration':
      fallbackRadius = 200; // Medium fallback for exploration mode
      break;
    case 'artistic':
      fallbackRadius = 150; // Smaller fallback for artistic mode
      break;
    default:
      fallbackRadius = 200;
  }

  const center = new THREE.Vector3(0, 0, 0);
  const size = new THREE.Vector3(fallbackRadius * 2, fallbackRadius * 2, fallbackRadius * 2);

  console.log('📊 Using fallback bounds:', {
    mode: mode,
    systemRadius: fallbackRadius,
    reason: 'No valid celestial bodies found'
  });

  return {
    min: new THREE.Vector3(-fallbackRadius, -fallbackRadius, -fallbackRadius),
    max: new THREE.Vector3(fallbackRadius, fallbackRadius, fallbackRadius),
    center,
    size,
    maxDimension: fallbackRadius * 2,
    systemRadius: fallbackRadius,
    furthestDistance: fallbackRadius,
    mode,
    isFallback: true
  };
}

/**
 * Smoothly animate camera to a target position and orientation
 */
export class CameraAnimator {
  constructor(camera, navigationControls = null) {
    this.camera = camera;
    this.navigationControls = navigationControls;
    this.isAnimating = false;
    this.animationId = null;
    this.onComplete = null;
    this.onProgress = null;

    // Animation parameters
    this.duration = 2000; // 2 seconds default
    this.easing = this.easeInOutCubic;
  }

  /**
   * Animate camera to target position and look-at point
   */
  animateTo(targetPosition, targetLookAt, duration = 2000, onComplete = null, onProgress = null) {
    // Cancel any existing animation
    this.stop();

    this.isAnimating = true;
    this.duration = duration;
    this.onComplete = onComplete;
    this.onProgress = onProgress;

    // Store initial state
    const startPosition = this.camera.position.clone();
    const startQuaternion = this.camera.quaternion.clone();

    // Calculate target quaternion by looking at the target
    const tempCamera = new THREE.PerspectiveCamera();
    tempCamera.position.copy(targetPosition);
    tempCamera.lookAt(targetLookAt);
    const targetQuaternion = tempCamera.quaternion.clone();

    // Pause navigation controls during animation
    if (this.navigationControls) {
      this.navigationControls.pause();
    }

    const startTime = performance.now();

    const animate = (currentTime) => {
      if (!this.isAnimating) return;

      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / this.duration, 1);
      const easedProgress = this.easing(progress);

      // Interpolate position
      this.camera.position.lerpVectors(startPosition, targetPosition, easedProgress);

      // Interpolate rotation
      this.camera.quaternion.slerpQuaternions(startQuaternion, targetQuaternion, easedProgress);

      // Call progress callback
      if (this.onProgress) {
        this.onProgress(progress);
      }

      if (progress >= 1) {
        // Animation complete
        this.stop();
        
        // Sync navigation controls with final camera state
        if (this.navigationControls && this.navigationControls.syncEulerWithCamera) {
          this.navigationControls.syncEulerWithCamera();
        }

        if (this.onComplete) {
          this.onComplete();
        }
      } else {
        this.animationId = requestAnimationFrame(animate);
      }
    };

    this.animationId = requestAnimationFrame(animate);
  }

  /**
   * Stop the current animation
   */
  stop() {
    this.isAnimating = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }

    // Resume navigation controls
    if (this.navigationControls) {
      this.navigationControls.resume();
    }
  }

  /**
   * Ease-in-out cubic easing function
   */
  easeInOutCubic(t) {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  }

  /**
   * Check if animation is currently running
   */
  isRunning() {
    return this.isAnimating;
  }
}

/**
 * Get the furthest celestial body from the center for bounds calculation
 */
export function getFurthestBody(celestialBodies) {
  let furthestDistance = 0;
  let furthestBody = null;

  celestialBodies.forEach(body => {
    if (body.name === 'sun') return; // Skip the sun

    const position = body.getPosition();
    const distance = position.length(); // Distance from origin (sun)

    if (distance > furthestDistance) {
      furthestDistance = distance;
      furthestBody = body;
    }
  });

  return { body: furthestBody, distance: furthestDistance };
}

/**
 * Calculate camera position for different viewing modes
 */
export function calculateViewModePosition(bounds, mode = 'overview') {
  const { center, maxDimension } = bounds;

  switch (mode) {
    case 'overview':
      // Standard overview position
      return calculateOptimalCameraPosition(bounds, { fov: 75 }, 1.2);

    case 'wide':
      // Wider view with more padding
      return calculateOptimalCameraPosition(bounds, { fov: 75 }, 1.8);

    case 'tight':
      // Closer view with less padding
      return calculateOptimalCameraPosition(bounds, { fov: 75 }, 1.0);

    case 'top':
      // Top-down view
      return {
        position: new THREE.Vector3(center.x, center.y + maxDimension, center.z),
        lookAt: center
      };

    case 'side':
      // Side view
      return {
        position: new THREE.Vector3(center.x + maxDimension, center.y, center.z),
        lookAt: center
      };

    default:
      return calculateOptimalCameraPosition(bounds, { fov: 75 }, 1.2);
  }
}
