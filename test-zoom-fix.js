/**
 * Test script for the fixed zoom-to-fit functionality
 * Run this in the browser console after the solar system loads
 */

// Test zoom-to-fit in different modes
async function testZoomToFitInAllModes() {
  console.log('🧪 Testing zoom-to-fit in all visualization modes...');
  
  // Get the app instance
  const app = window.solarSystemApp;
  if (!app) {
    console.error('❌ Solar system app not found. Make sure the app is loaded.');
    return;
  }
  
  const sceneManager = app.sceneManager;
  const uiManager = app.uiManager;
  
  if (!sceneManager || !uiManager) {
    console.error('❌ Scene manager or UI manager not found.');
    return;
  }
  
  // Test in each mode
  const modes = ['exploration', 'artistic', 'realistic'];
  
  for (const mode of modes) {
    console.log(`\n🎨 Testing zoom-to-fit in ${mode} mode...`);
    
    // Switch to the mode
    await sceneManager.setVisualizationMode(mode);
    
    // Wait a moment for the mode switch to complete
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test zoom-to-fit
    try {
      console.log(`🔍 Starting zoom-to-fit in ${mode} mode...`);
      await sceneManager.zoomToFit(1500); // 1.5 seconds for quick test
      console.log(`✅ Zoom-to-fit completed successfully in ${mode} mode`);
      
      // Check if orbit lines are visible
      const orbitLinesVisible = sceneManager.areOrbitLinesVisible();
      console.log(`🛸 Orbit lines visible: ${orbitLinesVisible}`);
      
      // Log camera position
      const cameraPos = sceneManager.camera.position;
      console.log(`📹 Camera position: [${cameraPos.x.toFixed(2)}, ${cameraPos.y.toFixed(2)}, ${cameraPos.z.toFixed(2)}]`);
      
    } catch (error) {
      console.error(`❌ Zoom-to-fit failed in ${mode} mode:`, error);
    }
    
    // Wait before next test
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('\n🎉 Zoom-to-fit testing completed!');
}

// Test bounds calculation directly
function testBoundsCalculation() {
  console.log('🧪 Testing bounds calculation...');
  
  const app = window.solarSystemApp;
  if (!app) {
    console.error('❌ Solar system app not found.');
    return;
  }
  
  const sceneManager = app.sceneManager;
  const celestialBodies = sceneManager.getAllCelestialBodies();
  
  console.log(`📊 Found ${celestialBodies.length} celestial bodies`);
  
  // Test bounds in each mode
  const modes = ['exploration', 'artistic', 'realistic'];
  
  modes.forEach(mode => {
    console.log(`\n📏 Testing bounds calculation in ${mode} mode:`);
    
    // Import and test the bounds calculation function
    import('./src/js/utils/CameraUtils.js').then(({ calculateSolarSystemBounds }) => {
      const bounds = calculateSolarSystemBounds(celestialBodies, true, mode);
      
      console.log(`   System radius: ${bounds.systemRadius.toFixed(2)} units`);
      console.log(`   Furthest distance: ${bounds.furthestDistance.toFixed(2)} units`);
      console.log(`   Max dimension: ${bounds.maxDimension.toFixed(2)} units`);
      console.log(`   Mode: ${bounds.mode}`);
    }).catch(error => {
      console.error(`❌ Error testing bounds in ${mode} mode:`, error);
    });
  });
}

// Test orbit lines toggle
function testOrbitLinesToggle() {
  console.log('🧪 Testing orbit lines toggle...');
  
  const app = window.solarSystemApp;
  if (!app) {
    console.error('❌ Solar system app not found.');
    return;
  }
  
  const sceneManager = app.sceneManager;
  
  // Check initial state
  const initialState = sceneManager.areOrbitLinesVisible();
  console.log(`🛸 Initial orbit lines state: ${initialState}`);
  
  // Toggle orbit lines
  const newState = sceneManager.toggleOrbitLines();
  console.log(`🛸 After toggle: ${newState}`);
  
  // Toggle back
  const finalState = sceneManager.toggleOrbitLines();
  console.log(`🛸 After second toggle: ${finalState}`);
}

// Test keyboard shortcut
function testKeyboardShortcut() {
  console.log('🧪 Testing keyboard shortcut (F key)...');
  
  const event = new KeyboardEvent('keydown', {
    key: 'f',
    code: 'KeyF',
    bubbles: true
  });
  
  document.dispatchEvent(event);
  console.log('⌨️ F key event dispatched');
}

// Test UI button
function testUIButton() {
  console.log('🧪 Testing UI button...');
  
  const button = document.getElementById('zoom-to-fit-button');
  if (!button) {
    console.error('❌ Zoom-to-fit button not found.');
    return;
  }
  
  console.log('✅ Button found, clicking...');
  button.click();
}

// Test edge cases
async function testEdgeCases() {
  console.log('🧪 Testing edge cases...');

  const app = window.solarSystemApp;
  if (!app) {
    console.error('❌ Solar system app not found.');
    return;
  }

  const sceneManager = app.sceneManager;

  // Test with no celestial bodies (simulate edge case)
  console.log('📊 Testing bounds calculation with empty array...');
  try {
    const { calculateSolarSystemBounds } = await import('./src/js/utils/CameraUtils.js');
    const emptyBounds = calculateSolarSystemBounds([], true, 'exploration');
    console.log('✅ Empty bounds handled correctly:', emptyBounds);
  } catch (error) {
    console.error('❌ Error with empty bounds:', error);
  }

  // Test zoom-to-fit multiple times rapidly
  console.log('🔄 Testing rapid zoom-to-fit calls...');
  try {
    const promises = [];
    for (let i = 0; i < 3; i++) {
      promises.push(sceneManager.zoomToFit(500)); // Very fast animations
    }
    await Promise.allSettled(promises);
    console.log('✅ Rapid zoom-to-fit calls handled correctly');
  } catch (error) {
    console.error('❌ Error with rapid calls:', error);
  }

  // Test in different modes with extreme camera positions
  console.log('📹 Testing from extreme camera positions...');
  const extremePositions = [
    { x: 10000, y: 10000, z: 10000 },
    { x: -5000, y: -5000, z: -5000 },
    { x: 0.1, y: 0.1, z: 0.1 }
  ];

  for (const pos of extremePositions) {
    sceneManager.camera.position.set(pos.x, pos.y, pos.z);
    console.log(`🎯 Testing from position [${pos.x}, ${pos.y}, ${pos.z}]`);

    try {
      await sceneManager.zoomToFit(800);
      const finalPos = sceneManager.camera.position;
      console.log(`✅ Zoom successful, final position: [${finalPos.x.toFixed(2)}, ${finalPos.y.toFixed(2)}, ${finalPos.z.toFixed(2)}]`);
    } catch (error) {
      console.error(`❌ Zoom failed from extreme position:`, error);
    }

    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

// Run comprehensive test
async function runComprehensiveTest() {
  console.log('🚀 Running comprehensive zoom-to-fit test...');

  // Test bounds calculation
  testBoundsCalculation();

  // Wait a moment
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Test orbit lines
  testOrbitLinesToggle();

  // Wait a moment
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Test edge cases
  await testEdgeCases();

  // Wait a moment
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Test zoom-to-fit in all modes
  await testZoomToFitInAllModes();

  console.log('🎉 Comprehensive test completed!');
}

// Export functions for manual testing
window.testZoomToFitInAllModes = testZoomToFitInAllModes;
window.testBoundsCalculation = testBoundsCalculation;
window.testOrbitLinesToggle = testOrbitLinesToggle;
window.testKeyboardShortcut = testKeyboardShortcut;
window.testUIButton = testUIButton;
window.testEdgeCases = testEdgeCases;
window.runComprehensiveTest = runComprehensiveTest;

console.log('🧪 Zoom-to-fit fix test functions loaded.');
console.log('Available functions:');
console.log('- testZoomToFitInAllModes()');
console.log('- testBoundsCalculation()');
console.log('- testOrbitLinesToggle()');
console.log('- testKeyboardShortcut()');
console.log('- testUIButton()');
console.log('- testEdgeCases()');
console.log('- runComprehensiveTest()');
